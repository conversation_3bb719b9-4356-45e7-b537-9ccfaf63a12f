# 武将

## 开脉

* 冲脉:打通冲脉需要龙灵仙凤丹x1、粮食x1210000、潜能x12100000，你确定要打通吗？
* 带脉:打通带脉需要龙灵仙凤丹x2、粮食x1440000、潜能x14400000，你确定要打通吗？
* 阴维脉:打通阴维脉需要龙灵仙凤丹x4、粮食x1690000、潜能x16900000，你确定要打通吗？
* 阳维脉:打通阳维脉需要龙灵仙凤丹x6、粮食x1960000、潜能x19600000，你确定要打通吗？
* 阴跷脉:打通冲脉需要龙灵仙凤丹x8、粮食x33750000和潜能x337500000，你确定要打通吗？
* 阳跷脉:打通阳跷脉需要龙灵仙凤丹x10、粮食x40960000、潜能x409600000，你确定要打通吗？
* 任脉:打通任脉需要龙灵仙凤丹x15、粮食x49130000、潜能x491300000，你确定要打通吗？
* 督脉:打通督脉需要龙灵仙凤丹x30、粮食x58320000、潜能x583200000，你确定要打通吗？*

## 魂魄

打通魂魄[金]魂魄灵珠[金]x10、潜能x133100000、粮食x133100000、木材x133100000、生铁x133100000、石料x133100000，你确定要打通吗？

# 士兵

# NPC

间隔缓存，查看详情时，如果有怪物缓存就显示怪物缓存。

# 战场

创建战场时，如果有怪物缓存 就显示怪物缓存

## 怪物缓存设定

创建战场时，添加怪物缓存和显示间隔缓存，根据显示间隔隐藏怪物，战斗失败删掉间隔缓存，

## 战斗结束的操作

清理怪物缓存,如果攻方失败 就清理间隔缓存

# 物品

# 庄园

## 人口

每间房屋的人口=房屋等级*20+10

## 建筑

建筑数=庄园等级*4+9

事件数=庄园等级*2+3

# 杂项

## 别人能查看的页面

我的、队伍-合并、武将部-合并、武将梯队-合并、武将详情、武将装备列表、装备详情、武将坐骑-合并、坐骑装备列表、坐骑装备详情、士兵详情、
挂售物品-合并、挂售物品详情、赠送物品列表

## 给角色添加物品

如果是装备，把装备添加的攻击力、防御力、生命、耐久、孔、要求等级，一起复制

装备类型不需要判断 能不能使用，应为本身type已经决定了

## 路由相关

同一个路由多次点击默认，跟新到最后一次点击的参数，updateRouter设置为false是，则不更新
