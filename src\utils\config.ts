import { RouterType, RouterListType } from "./types";
// <style>.npc{color:#FF4F0F}
//     .user{color:#FFA673}
//     .fight{color:#008000}
//     .status{color:#7F55B1}
//     .goods{color:#9B7EBD}
//     .task{color:#F49BAB}
//     .chat{color:#FFE1E0}
//     .general{color:#8F87F1}
//     .manor{color:#C68EFD}
//     .fight{color:#E9A5F1}
//     .team{color:#FED2E2}
//     .settingMenu{color:#5F8B4C}
//     .left{color:#C68EFD}
//     .right{color:#C68EFD}
//     .top{color:#C68EFD}
//     .bottom{color:#C68EFD}
//     </style>
const defaultConfig = {
    htmlHeader: `<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>永恒帝王</title>
    <style>.npc{color:#FF4F0F}
    </style>
    </head><body style="background-color:#FFFAE8; font-family: Arial, sans-serif;padding: 20px; margin: 0;">`,
    htmlfooter: `</body></html>`,
    sendRequest: `
        <script>
            function sendRequest(url){
                const formData = new FormData(document.querySelector('form'));
                let jsonObject = {};
                formData.forEach((value, key) => {
                    jsonObject[key] = Number(value) || value;
                });
                fetch(url, {
                    method: 'POST',
                    headers: {
                        "Content-type": "application/json; charset=utf-8",
                    },
                    body: JSON.stringify(jsonObject) 
                }).then(response => response.json())
                .then(res => {
                    if(res.msg){
                        let errorDiv = document.getElementById('error');
                        errorDiv.innerHTML = res.msg;
                        errorDiv.style.display ='block';
                    }else{
                        location.href = res.url;
                    }

                }).catch(error => {
                    console.error('Error:', error); 
                });
            }
        </script>
    `,
    duihao: 'data:image/gif;base64,R0lGODlhCAAQANUtAOTk8QCIRACBP2a1Xqus01urdtTq1gdvVRCJSzGfWCWTUQF5QySaTfD39JewvvLy+OLy6aDPt1lon0NOkgB/P9Dn27fct8Dh0MDg0PX69VCsfnJ0tXi+cUFjd6DQuHC2kydnaJ2ey1ybZ0uAjWS5jHC+j0CebvH59GG1czWGcWahlziiUgSMRv///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAAC0ALAAAAAAIABAAAAYywJZwSCwahaYjxahZGAOppzFxKFoYoyJHMUS0DAOQ8CNgoQaSYXklKrJWHeNqYixshkEAOw==',
    chahao: 'data:image/gif;base64,R0lGODlhCAAQAOZBANxkDM7FzuKRXNVeEupwA/fh09lhDuFoCeeaY2U9SvnNpls4TeaXY9hgD+J4KvW3gfChTY98j/S+iPOybvLNtu+AHPvt4dhoIOCGTf318O6cRNZfEMBYEuqDIH84JPXXxPW2ep5bOddfEPTQtpmBi+dtBeeMR8piDtlhD/jQp+13CeeIPumYSNxzLux0BbtfIuBmCqJ1aeCHTOecZO2xifOhVuRqB6iaqPPx8++TQqlUHO99FNVeEcmjj/748tpiDvSnYv///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAAEEALAAAAAAIABAAAAdPgEGCg4QCAwMCgxciKAYNPBRBPx4LCRwALQYHIYM2AA4lKoMEB0EPhC4vhEAVJySEOzoRhEEEtEEmMLcyG7cjtykSGQy0HSszhBMaLAiCgQA7',
}
function filterRouter(routerList: RouterListType, router: RouterType) {
    if (router.name == 'home') return routerList
    for (let index = 0; index < routerList.length; index++) {
        const element = routerList[index];
        if (element.name == router.name && element.service == router.service) {
            if(router.params.updateRouter!=false){
                element.params=router.params//把最后路由的参数赋值给当前路由
            }
            return routerList.slice(0, index + 1)
        }
    }
    routerList.push({ name: router.name, service: router.service, params: router.params, title: router.title })

    return routerList
}
const equipmentType = [
    { id: 30, name: '兵器' },
    { id: 31, name: '帽子' },
    { id: 32, name: '衣服' },
    { id: 33, name: '裤子' },
    { id: 34, name: '鞋子' },
    { id: 35, name: '项链' },
    { id: 36, name: '戒指' },
    { id: 37, name: '手套' },
    { id: 38, name: '肩甲' },
    { id: 39, name: '披风' },
    { id: 40, name: '特殊戒指' },
    { id: 41, name: '左手套' },
    { id: 42, name: 'PK盾' },
]
const equipmentMountType = [
    { id: 51, name: '蹄铁' },
    { id: 52, name: '马鞍' },
    { id: 53, name: '缰绳' },
    { id: 54, name: '马铠' },
    { id: 55, name: '马蹬' },
    { id: 56, name: '马嚼' },
]
const goodsType = [
    //1:消耗品 2:兵器 3:防具 4:士兵兵器 5:士兵防具 6:坐骑装备 7:装备宝石 8:其它 
    { id: 1, name: '消耗品' },
    {
        id: 2, name: '兵器',
        child: [
            //21:刀  22:剑  23:棍  24:斧  25:锤  26:枪
            { id: 21, name: '刀' },
            { id: 22, name: '剑' },
            { id: 23, name: '棍' },
            { id: 24, name: '斧' },
            { id: 25, name: '锤' },
            { id: 26, name: '枪' },
        ]
    },
    {
        id: 3, name: '防具',
        child: [
            //31:帽子  32:衣服  33:裤子  34:鞋子  35:项链  36:戒指  37:手套  38:肩甲  39:披风  40:特殊戒指 41:左手套 42:PK盾
            { id: 31, name: '帽子' },
            { id: 32, name: '衣服' },
            { id: 33, name: '裤子' },
            { id: 34, name: '鞋子' },
            { id: 35, name: '项链' },
            { id: 36, name: '戒指' },
            { id: 37, name: '手套' },
            { id: 38, name: '肩甲' },
            { id: 39, name: '披风' },
            { id: 40, name: '特殊戒指' },
            { id: 41, name: '左手套' },
            { id: 42, name: 'PK盾' },
        ]
    },
    {
        id: 4, name: '士兵兵器',
        child: [
            //21:刀  22:剑  23:棍  24:斧  25:锤  26:枪
            { id: 21, name: '刀' },
            { id: 22, name: '剑' },
            { id: 23, name: '棍' },
            { id: 24, name: '斧' },
            { id: 25, name: '锤' },
            { id: 26, name: '枪' },
        ], typeId: 'bingqiId', typeName: 'bingqiName'
    },
    {
        id: 5, name: '士兵防具', child: [
            //31:帽子  32:衣服  33:裤子  34:鞋子  35:项链  36:戒指  37:手套  38:肩甲  39:披风  40:特殊戒指 41:左手套 42:PK盾
            { id: 31, name: '帽子', typeId: 'maoziId', typeName: 'maoziName' },
            { id: 32, name: '衣服', typeId: 'yifuId', typeName: 'yifuName' },
            { id: 33, name: '裤子', typeId: 'kuziId', typeName: 'kuziName' },
            { id: 34, name: '鞋子', typeId: 'xieziId', typeName: 'xieziName' },
            { id: 35, name: '项链', typeId: 'xianlianId', typeName: 'xianlianName' },
            { id: 36, name: '戒指', typeId: 'jieziId', typeName: 'jieziName' },
            { id: 37, name: '手套', typeId: 'shougaoId', typeName: 'shougaoName' },
            { id: 38, name: '肩甲', typeId: 'jianjiaId', typeName: 'jianjiaName' },
            { id: 39, name: '披风', typeId: 'pifengId', typeName: 'pifengName' },
            { id: 40, name: '特殊戒指', typeId: 'jieshuId', typeName: 'jieshuName' },
            { id: 41, name: '左手套', typeId: 'zuoshoutaoId', typeName: 'zuoshoutaoName' },
            { id: 42, name: 'PK盾', typeId: 'pkdunId', typeName: 'pkdunName' },
        ]
    },
    {
        id: 6, name: '坐骑装备', child: [
            //51:蹄铁 52:马鞍 53:缰绳 54:马铠 55:马蹬 56:马嚼
            { id: 51, name: '蹄铁' },
            { id: 52, name: '马鞍' },
            { id: 53, name: '缰绳' },
            { id: 54, name: '马铠' },
            { id: 55, name: '马蹬' },
            { id: 56, name: '马嚼' },
        ]
    },
    { id: 7, name: '装备宝石' },
    {
        id: 8, name: '其它', child: [
            //71:商城 72:强化 73:材料 74:训练 75:任务 76:活动 77:神兵
            { id: 71, name: '商城' },
            { id: 72, name: '强化' },
            { id: 73, name: '材料' },
            { id: 74, name: '训练' },
            { id: 75, name: '任务' },
            { id: 76, name: '活动' },
            { id: 77, name: '神兵' },
            { id: 78, name: '其它' },
        ]
    },
]
const goodsType1 = [
    //1:消耗品 2:兵器 3:防具 4:士兵兵器 5:士兵防具 6:坐骑装备 7:装备宝石 8:其它 
    { id: 1, name: '消耗品' },
    {
        id: 8, name: '其它', child: [
            //71:商城 72:强化 73:材料 74:训练 75:任务 76:活动 77:神兵
            { id: 71, name: '商城' },
            { id: 72, name: '强化' },
            { id: 73, name: '材料' },
            { id: 74, name: '训练' },
            { id: 75, name: '任务' },
            { id: 76, name: '活动' },
            { id: 77, name: '神兵' },
            { id: 78, name: '其它' },
        ]
    },
]
//随机整数
function getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
//随机数
/**
 * 
 * @param min 最小值
 * @param max 最大值
 * @returns 
 */
function getRandomNumber(min, max) {
    return Math.random() * (max - min) + min;
}
// 建筑配置
const buildconfig = {
    //粮草 木材 石料 生铁 银两 时间
    //石料 生铁减半 2 2 1 1 2 2
    'zhuangyuan': {
        val: [100, 300, 1000, 5000, 30000, 100000, 400000,1000000,3000000,10000000],
        time: [100, 300, 1000, 5000, 30000, 80000, 200000,500000,1000000,2000000]
    },
    'fangwu': {
        time: [65, 75, 110, 310, 1560, 4060],
    },
    // 3 1
    'nongtian': {
        time: [90, 150, 360, 1560, 9060, 24060],
    },
    //类型对应的type值
    type: {
        'zhuangyuan': 1,
        'fangwu': 2,
        'nongtian': 3,
        'famu': 4,
        'caishi': 5,
        'tiekuang': 6,
        'tiejiang': 7,
        'caifeng': 8,
        'shuyuan': 9,
        'bingbu': 10,
        'mapeng': 11,
    }
}
const buildType = {
    zhuangyuan: {
        desc: '此建筑是建造其它任何建筑的前提。等级越高，可建造的建筑数量、同时可以进行的事件就越多。',
        name: '庄院',
        type: 1
    },
    fangwu: {
        desc: '房屋提升人口。',
        name: '房屋',
        type: 2
    },
    nongtian: {
        desc: '可以产出粮食。',
        name: '农田',
        type: 3
    },
    famu: {
        desc: '可以产出木材。',
        name: '伐木场',
        type: 4
    },
    caishi: {
        desc: '可以产出石料。',
        name: '采石场',
        type: 5
    },
    tiekuang: {
        desc: '可以产出生铁。',
        name: '铁矿场',
        type: 6
    },
    tiejiang: {
        desc: '铁匠铺用于打造各种兵器。',
        name: '铁匠铺',
        type: 7
    },
    caifeng: {
        desc: '裁缝铺用于制作各种防具。',
        name: '裁缝铺',
        type: 8
    },
    shuyuan: {
        desc: '研究生产工艺的地方。其研究的技能可以使自己资源场增产、加快各种建造需要的时间等。',
        name: '书院',
        type: 9
    },
    bingbu: {
        desc: '研究军事技能的地方。其研究的技能可以使自己的武将、士兵战斗能力加强。',
        name: '兵部',
        type: 10
    },
    mapeng: {
        desc: '饲养坐骑的地方。',
        name: '马棚',
        type: 11
    }
}
function handlbuildfn(type, lvl) {
    const obj = buildType
    if (type === 'zhuangyuan') {
        let val = buildconfig.zhuangyuan.val[lvl - 1]
        return {
            food: val,
            wood: val,
            stone: val / 2,
            iron: val / 2,
            gold: val,
            desc: obj[type].desc,
            name: obj[type].name,
            time: val
        }
    }
    if (type === 'fangwu') {
        let val = (buildconfig.zhuangyuan.val[lvl - 1]) / 10
        return {
            food: val,
            wood: val,
            stone: val / 2,
            iron: val / 2,
            gold: val,
            desc: obj[type].desc,
            name: obj[type].name,
            time: buildconfig.fangwu.time[lvl - 1]
        }
    }
    if (['nongtian', 'famu', 'caishi', 'tiekuang'].includes(type)) {
        let val = (buildconfig.zhuangyuan.val[lvl - 1]) / 10
        return {
            food: type === 'nongtian' ? val * 3 : val,
            wood: type === 'famu' ? val * 3 : val,
            stone: type === 'caishi' ? val * 3 : val,
            iron: type === 'tiekuang' ? val * 3 : val,
            gold: val,
            desc: obj[type].desc,
            name: obj[type].name,
            time: buildconfig.nongtian.time[lvl - 1]
        }
    }
    let val = (buildconfig.zhuangyuan.val[lvl - 1]) / 2
    return {
        food: val,
        wood: val,
        stone: val / 2,
        iron: val / 2,
        gold: val,
        desc: obj[type].desc,
        name: obj[type].name,
        time: val
    }
}
//建筑等级对应的建筑数、事件数
function manorEventfn(lvl) {
    const build = () => {
        return 9 +lvl* 4
    }
    const event = () => {
        return 3 + lvl* 2
    }
    return {
        buildNUm: build(),
        eventNum: event(),
    }
}
//毫秒换算成天时分秒
function formatDuring(mss) {
    var days = Math.floor(mss / (3600 * 24 * 1000));
    var hours = Math.floor((mss % (3600 * 24 * 1000)) / (3600 * 1000));
    var minutes = Math.floor((mss % (3600 * 1000)) / (60 * 1000));
    var seconds = Math.floor((mss % (60 * 1000)) / 1000);
    var result = "";
    if (days > 0) {
        result += days + "天";
    }
    if (hours > 0) {
        result += hours + "时";
    }
    if (minutes > 0) {
        result += minutes + "分";
    }
    if (seconds > 0 || result === "") {
        result += (seconds < 0 ? 0 : seconds) + "秒";
    }
    return result
}
//处理时间 [Fri Jul 05 2024 16:36:33 GMT+0800 (中国标准时间)] => 07-05 16:36:33
function formatDate(timeString, type = 1) {
    const date = new Date(timeString);
    const month = date.getMonth() + 1; // 月份是从0开始的，所以需要加1
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    function addZero(num) {
        return num < 10 ? "0" + num : num;
    }
    let str = ''
    type == 1 && (str = addZero(month) + "-" + addZero(day) + " " + addZero(hours) + ":" + addZero(minutes))
    type == 2 && (str = addZero(hours) + ":" + addZero(minutes))
    return str
}
//字符串截取
function extractBetween(str, start, end) {
    if (!str) return ''
    let adjustedStartIndex = 0, adjustedEndIndex = str.length;
    if (start) {
        // 查找开始字符的位置
        const startIndex = str.indexOf(start);
        // 如果开始字符不存在，则从头开始
        adjustedStartIndex = startIndex + start.length;
    }

    if (end) {
        // 从调整后的开始位置查找结束字符的位置
        const endIndex = str.indexOf(end, adjustedStartIndex);
        // 如果结束字符不存在，则截取到字符串末尾
        adjustedEndIndex = endIndex;
    }


    // 返回开始字符（或字符串开头）和结束字符（或字符串末尾）之间的字符串
    return str.substring(adjustedStartIndex, adjustedEndIndex);
}
// 技能模板   名称/伤害系数/攻击距离/攻击数量/武器类型/恢复时间
const skillTemplate = {
    "puTongGongJi": ["普通攻击", 2, 1, 1, 0],
    "daDaoKanShu": ["大刀砍术", 66, 2, 2, 21],
    "yanLingDaoFa": ["雁翎刀法", 66, 3, 2, 21],
    "luanGun": ["乱棍", 40, 2, 2, 23],
    "wuShengDaoFa": ["无声刀法", 30, 3, 2, 21],
    "yuXinJianFa": ["玉心剑法", 71, 2, 3, 22],
    "daHuGunFa": ["打虎棍法", 70, 3, 2, 23],
    "baWangJianFa": ["霸王剑法", 72, 4, 5, 22],
    "xiaoXiaoLuoMuDaoFa": ["萧萧落木刀法", 96, 4, 5, 21],
    "daLiJinGangGunFa": ["大力金刚棍法", 84, 3, 2, 23],
    "chaiJiaQiangFa": ["柴家枪法", 76, 4, 2, 26],
    "chuiYunPoFengQiangFa": ["吹云破风枪法", 76, 3, 3, 26],
    "chiMuDaoFa": ["赤目刀法", 84, 3, 2, 21],
    "ruoShuiDaoFa": ["弱水刀法", 45, 3, 2, 21],
    "yangJiaWuHeQiang": ["杨家五合枪", 45, 4, 3, 26],
    "maoShanDaoJian": ["茅山道剑", 42, 2, 2, 22],
    "tianJiJianFa": ["天机剑法", 45, 3, 4, 22],
    "zuiDao": ["醉刀", 75, 4, 1, 21],
    "daLiBanFu": ["大力板斧", 75, 2, 2, 24],
    "zhuFuJianFa": ["祝福剑法", 80, 2, 5, 22],
    "zhuFuDaoFa": ["祝福刀法", 80, 2, 5, 21],
    "kuaiGong": ["快攻", 30, 1, 1, 0],
    "daFuKanShu": ["大斧砍术", 10, 1, 1, 24],
    "ciQiangFa": ["刺枪法", 68, 3, 3, 26],
    "jingKanDaoFa": ["精砍刀法", 30, 3, 1, 21],
    "jingCiJianFa": ["精刺剑法", 72, 3, 2, 22],
    "suNvJianFa": ["素女剑法", 71, 2, 1, 22],
    "daLiGunFa": ["大力棍法", 84, 2, 2, 23],
    "daLiDaoFa": ["大力刀法", 84, 1, 1, 21],
    "chongFengQiangFa": ["冲锋枪法", 76, 4, 2, 26],
    "daLiJianFa": ["大力剑法", 73, 3, 3, 22],
    "jinLiYiJi": ["尽力一击", 84, 4, 2, 0],
    "tieJiaQiang": ["铁甲枪", 45, 4, 3, 26],
    "muOuJianFa": ["木偶剑法", 42, 3, 5, 22],
    "shuiZhongJianFa": ["水中剑法", 45, 3, 4, 22],
    "shenXingGunFa": ["神行棍法", 45, 2, 2, 23],
    "changBiDao": ["长臂刀", 75, 5, 5, 21],
    "daLiFu": ["大力斧", 56, 1, 1, 24],
    "huBenChuiFa": ["虎奔锤法", 75, 3, 4, 25],
    "huNuJianFa": ["虎怒剑法", 75, 3, 4, 22],
    "huPuDaoFa": ["虎扑刀法", 75, 3, 4, 21],
    "huChongFuFa": ["虎冲斧法", 75, 3, 4, 24],
    "huHouQiangFa": ["虎吼枪法", 75, 3, 4, 26],
    "luanJian": ["乱剑", 50, 5, 5, 22],
    "kuangBaoGun": ["狂暴棍", 60, 2, 2, 23],
    "junFu": ["军斧", 60, 2, 2, 24],
    "zhongJiaQiangFa": ["重甲枪法", 87, 3, 2, 26],
    "feiYingJianFa": ["飞影剑法", 30, 3, 3, 22],
    "zhongChui": ["重锤", 30, 3, 2, 25],
    "shenDaoFa": ["神刀法", 70, 2, 2, 21],
    "pinMingYiJi": ["拼命一击", 80, 1, 3, 0],
    "feiDaoFa": ["飞刀法", 30, 1, 1, 21],
    "yuLinJianFa": ["御林剑法", 51, 3, 5, 22],
    "yuLinDaoFa": ["羽林刀法", 54, 2, 2, 21],
    "huangJinQiangFa": ["黄金枪法", 90, 3, 3, 26],
    "feiTianShenJian": ["飞天神剑", 180, 1, 2, 22],
    "niangZiJianFa": ["娘子剑法", 56, 2, 2, 22],
    "feiXianJianFa": ["飞仙剑法", 80, 3, 5, 22],
    "longYinJianFa": ["龙吟剑法", 84, 3, 6, 0],
    "jinGuangCiYanChui": ["金光刺眼锤", 100, 2, 3, 25],
    "luanYanGunFa": ["乱眼棍法", 90, 1, 4, 23],
    "chiYunQiangFa": ["赤云枪法", 95, 4, 2, 26, 52],
    "hengSaoQianJun": ["横扫千军", 130, 3, 4, 24],
    "suiLangDaoFa": ["随浪刀法", 110, 4, 2, 21],
    "duBiJianFa": ["独臂剑法", 90, 4, 1, 22],
    "lieHuoZhuiJi": ["烈火追击", 80, 1, 1, 0],
    "qingLongYanYueZhan": ["青龙偃月斩", 208, 4, 4, 0],
    "baiBuChuanYang": ["百步穿杨", 228, 3, 4, 0],
    "xuanGuangShuiLei": ["炫光水雷", 300, 4, 4, 0],
    "zhanChe": ["战车", 208, 4, 4, 0],
    "eMiTuoFo": ["阿弥陀佛", 208, 4, 4, 0],
    "tiTianSuanMing": ["替天算命", 200, 4, 4, 0],
    "suXinJianFa": ["素心剑法", 208, 4, 4, 22],
    "zhanPao": ["战炮", 200, 4, 4, 0],
    "shenBian": ["神鞭", 200, 4, 4, 0],
    "shuangQiang": ["双枪", 200, 4, 4, 26],
    "xueYinShou": ["血印手", 180, 4, 5, 0],
    "jiangLongShiBaZhang": ["降龙十八掌", 200, 5, 10, 0],
    "daGouBangFa": ["打狗棒法", 180, 3, 5, 23],
    "ganJiangJianPu": ["干将剑谱", 1, 1, 1, 0, 300],
    "moXieJianPu": ["莫邪剑谱", 1, 1, 1, 0, 300],
    "zhanGeLiZan": ["战歌礼赞", 1, 1, 3, 0, 300],
    "naHan": ["呐喊", 1, 1, 2, 0, 300,],
    "qianJunShaWeiGunFa": ["千钧杀威棍法", 120, 4, 5, 23, 90,'融汇重力与杀气的绝杀技，棍势如陨石坠地。第二式“千钧一发“可压弯城门，第四式“万钧之力“能劈开山涧。岳飞曾用此棍斩断金军帅旗，发髻散落时棍气凝成“还我河山“四字，至今仍镌刻在风波亭石碑上'],
    "qianDaoWanGua": ["千刀万剐", 172, 3, 3, 21, 60,'凌迟酷刑般的连续切割，刀刀专攻人体脆弱处。刀诀云：“千刀取骨，万剐剥皮。“明朝锦衣卫用此法处决大太监刘谨，三千六百刀后皮肉寸寸剥离，露出森森白骨，刀刃磨损过半仍滴血不沾'],
    // 名称/伤害系数/攻击距离/攻击数量/武器类型/恢复时间
}
//兵器类别
const weaponType = {
    21: '刀',
    22: '剑',
    23: '棍',
    24: '斧',
    25: '锤',
    26: '枪',
    0: '武器'
}
// 兵种模板 步兵
const soldierTemplate = {
    // 步兵
    bubing: {
        type: 'bubing',
        name: '步兵',
        attack: 10,
        defense: 20,
        hp: 520,
        addattack: 10,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制乱剑手，对乱剑手的伤害加倍',
        recruitFood: 30,
        recruitGold: 30,
        recruitTime: 24,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.kuaiGong],
        isInit: true,
    },
    // 刀斧手
    daoFuShou: {
        type: 'daoFuShou',
        name: '刀斧手',
        attack: 13,
        defense: 14,
        hp: 624,
        addattack: 13,
        adddefense: 14,
        addhp: 20,
        desc: '此兵种可以克制军斧兵，对军斧兵的伤害加倍。',
        recruitFood: 30,
        recruitGold: 30,
        recruitTime: 24,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.daDaoKanShu, skillTemplate.daFuKanShu],
        isInit: false,
    },
    // 乱棍军
    luanGongJun: {
        type: 'luanGongJun',
        name: '乱棍军',
        attack: 10,
        defense: 20,
        hp: 520,
        addattack: 10,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制骑兵，对骑兵的伤害加倍。',
        recruitFood: 24,
        recruitGold: 24,
        recruitTime: 22,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.luanGun],
        isInit: false,
    },
    // 刺枪兵
    ciQiangBing: {
        type: 'ciQiangBing',
        name: '刺枪兵',
        attack: 10,
        defense: 20,
        hp: 676,
        addattack: 10,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制狂暴棍手，对狂暴棍手的伤害加倍。',
        recruitFood: 34,
        recruitGold: 34,
        recruitTime: 26,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.ciQiangFa],
        isInit: true,
    },
    // 快刀手
    kuaiDaoShou: {
        type: 'kuaiDaoShou',
        name: '快刀手',
        attack: 11,
        defense: 20,
        hp: 728,
        addattack: 11,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制重甲枪兵，对重甲枪兵的伤害加倍。',
        recruitFood: 40,
        recruitGold: 40,
        recruitTime: 26,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.jingKanDaoFa],
        isInit: true,
    },
    // 快剑手
    kuaiJianShou: {
        type: 'kuaiJianShou',
        name: '快剑手',
        attack: 11,
        defense: 20,
        hp: 728,
        addattack: 11,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制飞剑手，对飞剑手的伤害加倍。',
        recruitFood: 40,
        recruitGold: 40,
        recruitTime: 26,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.jingCiJianFa],
        isInit: true,
    },
    // 金甲女兵
    jinjianvbing: {
        type: 'jinjianvbing',
        name: '金甲女兵',
        attack: 13,
        defense: 14,
        hp: 520,
        addattack: 10,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制禁军侍卫，对禁军侍卫的伤害加倍。',
        recruitFood: 20,
        recruitGold: 20,
        recruitTime: 20,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.suNvJianFa],
        isInit: true,
    },
    // 大力棍手
    daLiGunShou: {
        type: 'daLiGunShou',
        name: '大力棍手',
        attack: 13,
        defense: 24,
        hp: 780,
        addattack: 13,
        adddefense: 24,
        addhp: 20,
        desc: '此兵种可以克制重锤手，对重锤手的伤害加倍。',
        recruitFood: 50,
        recruitGold: 50,
        recruitTime: 26,
        population: 2,
        skill: [skillTemplate.puTongGongJi, skillTemplate.daLiGunFa],
        isInit: true,
    },
    // 大力刀手
    daLiDaoShou: {
        type: 'daLiDaoShou',
        name: '大力刀手',
        attack: 13,
        defense: 24,
        hp: 780,
        addattack: 13,
        adddefense: 24,
        addhp: 20,
        desc: '此兵种可以克制神刀兵，对神刀兵的伤害加倍。',
        recruitFood: 50,
        recruitGold: 50,
        recruitTime: 26,
        population: 2,
        skill: [skillTemplate.puTongGongJi, skillTemplate.daLiDaoFa],
        isInit: true,
    },
    // 冲锋枪兵
    chongFengQiangBing: {
        type: 'chongFengQiangBing',
        name: '冲锋枪兵',
        attack: 13,
        defense: 24,
        hp: 780,
        addattack: 13,
        adddefense: 24,
        addhp: 20,
        desc: '此兵种可以克制飞刀手，对飞刀手的伤害加倍。',
        recruitFood: 50,
        recruitGold: 50,
        recruitTime: 32,
        population: 2,
        skill: [skillTemplate.puTongGongJi, skillTemplate.chongFengQiangFa],
        isInit: true,
    },
    // 重剑手
    zhongJianShou: {
        type: 'zhongJianShou',
        name: '重剑手',
        attack: 16,
        defense: 24,
        hp: 780,
        addattack: 16,
        adddefense: 24,
        addhp: 20,
        desc: '此兵种可以克制敢死兵，对敢死兵的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 3,
        skill: [skillTemplate.puTongGongJi, skillTemplate.daLiJianFa],
        isInit: true,
    },
    // 狂暴步兵
    kuangBaoBuBing: {
        type: 'kuangBaoBuBing',
        name: '狂暴步兵',
        attack: 18,
        defense: 20,
        hp: 416,
        addattack: 18,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制陆行军，对陆行军的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 44,
        population: 3,
        skill: [skillTemplate.puTongGongJi, skillTemplate.jinLiYiJi],
        isInit: true,
    },
    // 铁甲枪兵
    tieJiaQiangBing: {
        type: 'tieJiaQiangBing',
        name: '铁甲枪兵',
        attack: 5,
        defense: 60,
        hp: 1040,
        addattack: 5,
        adddefense: 60,
        addhp: 20,
        desc: '此兵种可以克制御林剑客，对御林剑客的伤害加倍。',
        recruitFood: 80,
        recruitGold: 80,
        recruitTime: 60,
        population: 3,
        skill: [skillTemplate.puTongGongJi, skillTemplate.tieJiaQiang],
        isInit: true,
    },
    // 木偶兵
    muOuBing: {
        type: 'muOuBing',
        name: '木偶兵',
        attack: 30,
        defense: 8,
        hp: 208,
        addattack: 30,
        adddefense: 8,
        addhp: 20,
        desc: '此兵种可以克制赤甲斧头兵，对赤甲斧头兵的伤害加倍。',
        recruitFood: 80,
        recruitGold: 80,
        recruitTime: 60,
        population: 3,
        skill: [skillTemplate.puTongGongJi, skillTemplate.muOuJianFa],
        isInit: true,
    },
    // 水兵
    shuiBing: {
        type: 'shuiBing',
        name: '水兵',
        attack: 5,
        defense: 10,
        hp: 260,
        addattack: 5,
        adddefense: 10,
        addhp: 20,
        desc: '此兵种可以克制金刚棍兵，对金刚棍兵的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 56,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.shuiZhongJianFa],
        isInit: true,
    },
    // 神行棍兵
    shenXingGunBing: {
        type: 'shenXingGunBing',
        name: '神行棍兵',
        attack: 5,
        defense: 24,
        hp: 1560,
        addattack: 5,
        adddefense: 24,
        addhp: 20,
        desc: '此兵种可以克制黄金枪兵，对黄金枪兵的伤害加倍。',
        recruitFood: 80,
        recruitGold: 80,
        recruitTime: 60,
        population: 3,
        skill: [skillTemplate.puTongGongJi, skillTemplate.shenXingGunFa],
        isInit: true,
    },
    // 长臂刀兵
    changBiDaoBing: {
        type: 'changBiDaoBing',
        name: '长臂刀兵',
        attack: 20,
        defense: 40,
        hp: 520,
        addattack: 20,
        adddefense: 40,
        addhp: 20,
        desc: '此兵种可以克制羽林刀客，对羽林刀客的伤害加倍。',
        recruitFood: 90,
        recruitGold: 90,
        recruitTime: 66,
        population: 5,
        skill: [skillTemplate.puTongGongJi, skillTemplate.changBiDao],
        isInit: true,
    },
    // 大力斧头兵
    daLiFuTouBing: {
        type: 'daLiFuTouBing',
        name: '大力斧头兵',
        attack: 33,
        defense: 8,
        hp: 200,
        addattack: 33,
        adddefense: 8,
        addhp: 20,
        desc: '此兵种可以克制御林剑客，对御林剑客的伤害加倍。',
        recruitFood: 90,
        recruitGold: 90,
        recruitTime: 66,
        population: 5,
        skill: [skillTemplate.puTongGongJi, skillTemplate.daLiFu],
        isInit: true,
    },
    // 骑兵
    qiBing: {
        type: 'qiBing',
        name: '骑兵',
        attack: 16,
        defense: 20,
        hp: 520,
        addattack: 16,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制步兵，对步兵的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 乱剑手
    luanJianShou: {
        type: 'luanJianShou',
        name: '乱剑手',
        attack: 15,
        defense: 20,
        hp: 520,
        addattack: 15,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制狂暴步兵，对狂暴步兵的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.luanJian],
        isInit: false,
    },
    // 狂暴棍手
    kuangBaoGunShou: {
        type: 'kuangBaoGunShou',
        name: '狂暴棍手',
        attack: 15,
        defense: 20,
        hp: 520,
        addattack: 15,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制重剑手，对重剑手的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.kuangBaoGun],
        isInit: false,
    },
    // 军斧兵
    junFuBing: {
        type: 'junFuBing',
        name: '军斧兵',
        attack: 16,
        defense: 20,
        hp: 520,
        addattack: 16,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制冲锋枪兵，对冲锋枪兵的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.junFu],
        isInit: false,
    },
    // 重甲枪兵
    zhongJiaQiangBing: {
        type: 'zhongJiaQiangBing',
        name: '重甲枪兵',
        attack: 11,
        defense: 20,
        hp: 936,
        addattack: 11,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制快剑手，对快剑手的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.zhongJiaQiangFa],
        isInit: false,
    },
    // 飞剑手
    feiJianShou: {
        type: 'feiJianShou',
        name: '飞剑手',
        attack: 11,
        defense: 26,
        hp: 728,
        addattack: 11,
        adddefense: 26,
        addhp: 20,
        desc: '此兵种可以克制刺枪兵，对刺枪兵的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.feiYingJianFa],
        isInit: false,
    },
    // 重锤手
    zhongChuiShou: {
        type: 'zhongChuiShou',
        name: '重锤手',
        attack: 18,
        defense: 22,
        hp: 620,
        addattack: 18,
        adddefense: 22,
        addhp: 20,
        desc: '此兵种可以克制金甲女兵，对金甲女兵的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 2,
        skill: [skillTemplate.puTongGongJi, skillTemplate.zhongChui],
        isInit: false,
    },
    // 神刀兵
    shenDaoBing: {
        type: 'shenDaoBing',
        name: '神刀兵',
        attack: 21,
        defense: 22,
        hp: 610,
        addattack: 21,
        adddefense: 22,
        addhp: 20,
        desc: '此兵种可以克制乱棍军，对乱棍军的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 2,
        skill: [skillTemplate.puTongGongJi, skillTemplate.shenDaoFa],
        isInit: false,
    },
    // 敢死兵
    ganSiBing: {
        type: 'ganSiBing',
        name: '敢死兵',
        attack: 23,
        defense: 23,
        hp: 620,
        addattack: 23,
        adddefense: 23,
        addhp: 20,
        desc: '此兵种可以克制快剑手，对快剑手的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 3,
        skill: [skillTemplate.puTongGongJi, skillTemplate.pinMingYiJi],
        isInit: false,
    },
    // 飞刀手
    feiDaoShou: {
        type: 'feiDaoShou',
        name: '飞刀手',
        attack: 20,
        defense: 20,
        hp: 520,
        addattack: 20,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制刀斧手，对刀斧手的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 3,
        skill: [skillTemplate.puTongGongJi, skillTemplate.feiDaoFa],
        isInit: false,
    },
    // 金刚棍兵
    jinGangGunBing: {
        type: 'jinGangGunBing',
        name: '金刚棍兵',
        attack: 20,
        defense: 20,
        hp: 520,
        addattack: 20,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制狂暴步兵，对狂暴步兵的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 3,
        skill: [skillTemplate.puTongGongJi, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 陆行军
    luXingJun: {
        type: 'luXingJun',
        name: '陆行军',
        attack: 20,
        defense: 20,
        hp: 520,
        addattack: 20,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制重剑手，对重剑手的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 赤甲斧头兵
    chiJiaFuTouBing: {
        type: 'chiJiaFuTouBing',
        name: '赤甲斧头兵',
        attack: 7,
        defense: 60,
        hp: 1040,
        addattack: 7,
        adddefense: 60,
        addhp: 20,
        desc: '此兵种可以克制铁甲枪兵，对铁甲枪兵的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 3,
        skill: [skillTemplate.puTongGongJi, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 御林剑客
    yuLinJianKe: {
        type: 'yuLinJianKe',
        name: '御林剑客',
        attack: 39,
        defense: 8,
        hp: 208,
        addattack: 39,
        adddefense: 8,
        addhp: 20,
        desc: '此兵种可以克制木偶兵，对木偶兵的伤害加倍。',
        recruitFood: 120,
        recruitGold: 120,
        recruitTime: 90,
        population: 3,
        skill: [skillTemplate.puTongGongJi, skillTemplate.yuLinJianFa],
        isInit: false,
    },
    // 羽林刀客
    yuLinDaoKe: {
        type: 'yuLinDaoKe',
        name: '羽林刀客',
        attack: 5,
        defense: 24,
        hp: 2028,
        addattack: 5,
        adddefense: 24,
        addhp: 20,
        desc: '此兵种可以克制神行棍兵，对神行棍兵的伤害加倍。',
        recruitFood: 120,
        recruitGold: 120,
        recruitTime: 90,
        population: 3,
        skill: [skillTemplate.puTongGongJi, skillTemplate.yuLinDaoFa],
        isInit: false,
    },
    // 黄金枪兵
    huangJinQiangBing: {
        type: 'huangJinQiangBing',
        name: '黄金枪兵',
        attack: 26,
        defense: 52,
        hp: 780,
        addattack: 26,
        adddefense: 52,
        addhp: 20,
        desc: '此兵种可以克制长臂刀兵，对长臂刀兵的伤害加倍。',
        recruitFood: 135,
        recruitGold: 135,
        recruitTime: 100,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.huangJinQiangFa],
        isInit: false,
    },
    // 禁军侍卫
    jinJunShiWei: {
        type: 'jinJunShiWei',
        name: '禁军侍卫',
        attack: 52,
        defense: 6,
        hp: 156,
        addattack: 52,
        adddefense: 6,
        addhp: 20,
        desc: '此兵种可以克制大力斧头兵，对大力斧头兵的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.feiTianShenJian],
        isInit: false,
    },
    // 千剑娘子军
    qianJianNiangZiJun: {
        type: 'qianJianNiangZiJun',
        name: '千剑娘子军',
        attack: 22,
        defense: 20,
        hp: 620,
        addattack: 22,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制水兵，对水兵的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 40,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.niangZiJianFa],
        isInit: false,
    },
    // 仙兵
    xianBing: {
        type: 'xianBing',
        name: '仙兵',
        attack: 56,
        defense: 12,
        hp: 1040,
        addattack: 56,
        adddefense: 12,
        addhp: 20,
        desc: '',
        recruitFood: 150,
        recruitGold: 150,
        recruitTime: 160,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.feiXianJianFa],
        isInit: false,
    },
    // 古神兵
    guShenBing: {
        type: 'guShenBing',
        name: '古神兵',
        attack: 60,
        defense: 16,
        hp: 1300,
        addattack: 60,
        adddefense: 16,
        addhp: 20,
        desc: '',
        recruitFood: 250,
        recruitGold: 250,
        recruitTime: 320,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.feiXianJianFa], // Go 数据中是 feiXianJianFa
        isInit: false,
    },
    // 龙魄兵
    longPoBing: {
        type: 'longPoBing',
        name: '龙魄兵',
        attack: 64,
        defense: 24,
        hp: 1456,
        addattack: 64,
        adddefense: 24,
        addhp: 20,
        desc: '',
        recruitFood: 350,
        recruitGold: 350,
        recruitTime: 500,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.longYinJianFa],
        isInit: false,
    },
    // 金锤手
    jinChuiShou: {
        type: 'jinChuiShou',
        name: '金锤手',
        attack: 20,
        defense: 50,
        hp: 1144,
        addattack: 20,
        adddefense: 50,
        addhp: 20,
        desc: '此兵种可以克制赤甲斧头兵，对赤甲斧头兵的伤害加倍。 ',
        recruitFood: 500,
        recruitGold: 500,
        recruitTime: 300,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.jinGuangCiYanChui, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 黑金锤手
    heiJinChuiShou: {
        type: 'heiJinChuiShou',
        name: '黑金锤手',
        attack: 24,
        defense: 60,
        hp: 2288,
        addattack: 24,
        adddefense: 60,
        addhp: 20,
        desc: '此兵种可以克制龙魄兵，对龙魄兵的伤害加倍。',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.jinGuangCiYanChui, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 双节棍兵
    shuangJieGunBing: {
        type: 'shuangJieGunBing',
        name: '双节棍兵',
        attack: 18,
        defense: 64,
        hp: 572,
        addattack: 18,
        adddefense: 64,
        addhp: 20,
        desc: '此兵种可以克制重甲枪兵，对重甲枪兵的伤害加倍。 ',
        recruitFood: 500,
        recruitGold: 500,
        recruitTime: 300,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.luanYanGunFa, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 三节棍兵
    sanJieGunBing: {
        type: 'sanJieGunBing',
        name: '三节棍兵',
        attack: 21,
        defense: 128,
        hp: 728,
        addattack: 21,
        adddefense: 128,
        addhp: 20,
        desc: '此兵种可以克制羽林刀客，对羽林刀客的伤害加倍。',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.luanYanGunFa, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 三枪骑兵
    sanQiangQiBing: {
        type: 'sanQiangQiBing',
        name: '三枪骑兵',
        attack: 12,
        defense: 44,
        hp: 780,
        addattack: 12,
        adddefense: 44,
        addhp: 20,
        desc: '此兵种可以克制金锤手，对金锤手的伤害加倍。',
        recruitFood: 500,
        recruitGold: 500,
        recruitTime: 300,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.chiYunQiangFa, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 乱枪骑兵
    luanQiangQiBing: {
        type: 'luanQiangQiBing',
        name: '乱枪骑兵',
        attack: 36,
        defense: 50,
        hp: 914,
        addattack: 36,
        adddefense: 50,
        addhp: 20,
        desc: '此兵种可以克制黑金锤手，对黑金锤手的伤害加倍。',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.chiYunQiangFa, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 板斧重兵
    banFuZhongBing: {
        type: 'banFuZhongBing',
        name: '板斧重兵',
        attack: 14,
        defense: 70,
        hp: 884,
        addattack: 14,
        adddefense: 70,
        addhp: 20,
        desc: '此兵种可以克制羽林刀客，对羽林刀客的伤害加倍。 ',
        recruitFood: 500,
        recruitGold: 500,
        recruitTime: 300,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.hengSaoQianJun, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 双斧重骑兵
    shuangFuZhongQiBing: {
        type: 'shuangFuZhongQiBing',
        name: '双斧重骑兵',
        attack: 18,
        defense: 90,
        hp: 1014,
        addattack: 18,
        adddefense: 90,
        addhp: 20,
        desc: '此兵种可以克制禁军侍卫，对禁军侍卫的伤害加倍。 ',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.hengSaoQianJun, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 单刀客
    danDaoKe: {
        type: 'danDaoKe',
        name: '单刀客',
        attack: 12,
        defense: 52,
        hp: 780,
        addattack: 12,
        adddefense: 52,
        addhp: 20,
        desc: '此兵种可以克制三公子，对三公子的伤害加倍。',
        recruitFood: 500,
        recruitGold: 500,
        recruitTime: 300,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.suiLangDaoFa, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 双刀客
    shuangDaoKe: {
        type: 'shuangDaoKe',
        name: '双刀客',
        attack: 24,
        defense: 56,
        hp: 814,
        addattack: 24,
        adddefense: 56,
        addhp: 20,
        desc: '此兵种可以克制三公子，对三公子的伤害加倍。',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.suiLangDaoFa, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 独臂剑手
    duBiJianShou: {
        type: 'duBiJianShou',
        name: '独臂剑手',
        attack: 30,
        defense: 40,
        hp: 312,
        addattack: 30,
        adddefense: 40,
        addhp: 20,
        desc: '此兵种可以克制双节棍兵，对双节棍兵的伤害加倍。',
        recruitFood: 500,
        recruitGold: 500,
        recruitTime: 300,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.duBiJianFa, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 互博剑手
    huBoJianShou: {
        type: 'huBoJianShou',
        name: '互博剑手',
        attack: 60,
        defense: 44,
        hp: 416,
        addattack: 60,
        adddefense: 44,
        addhp: 20,
        desc: '此兵种可以克制双节棍兵，对双节棍兵的伤害加倍。',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.duBiJianFa, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 超水兵
    chaoShuiBing: {
        type: 'chaoShuiBing',
        name: '超水兵',
        attack: 22,
        defense: 44,
        hp: 1144,
        addattack: 22,
        adddefense: 44,
        addhp: 20,
        desc: '此兵种可以克制云儿士兵，对云儿士兵的伤害加倍。',
        recruitFood: 360,
        recruitGold: 360,
        recruitTime: 360,
        population: 2,
        skill: [skillTemplate.puTongGongJi, skillTemplate.shuiZhongJianFa],
        isInit: false,
    },
    // 烈火步兵
    lieHuoBuBing: {
        type: 'lieHuoBuBing',
        name: '烈火步兵',
        attack: 20,
        defense: 20,
        hp: 919,
        addattack: 20,
        adddefense: 20,
        addhp: 20,
        desc: '此兵种可以克制金锤手，对金锤手的伤害加倍。',
        recruitFood: 60,
        recruitGold: 60,
        recruitTime: 60,
        population: 1,
        skill: [skillTemplate.puTongGongJi, skillTemplate.lieHuoZhuiJi],
        isInit: false,
    },
    // 云儿
    yunEr: {
        type: 'yunEr',
        name: '云儿',
        attack: 90,
        defense: 8,
        hp: 416,
        addattack: 90,
        adddefense: 8,
        addhp: 20,
        desc: '此兵种可以克制羽林刀客，对羽林刀客的伤害加倍',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.qingLongYanYueZhan],
        isInit: false,
    },
    // 飞天云儿
    feiTianYunEr: {
        type: 'feiTianYunEr',
        name: '飞天云儿',
        attack: 92,
        defense: 12,
        hp: 520,
        addattack: 92,
        adddefense: 12,
        addhp: 20,
        desc: '此兵种可以克制羽林刀客，对羽林刀客的伤害加倍',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.qingLongYanYueZhan],
        isInit: false,
    },
    // 老兵
    laoBing: {
        type: 'laoBing',
        name: '老兵',
        attack: 92,
        defense: 6,
        hp: 312,
        addattack: 92,
        adddefense: 6,
        addhp: 20,
        desc: '此兵种可以克制云儿，对云儿的伤害加倍',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.baiBuChuanYang],
        isInit: false,
    },
    // 燕青
    yanQing: {
        type: 'yanQing',
        name: '燕青',
        attack: 80,
        defense: 10,
        hp: 520,
        addattack: 80,
        adddefense: 10,
        addhp: 20,
        desc: '此兵种可以克制老兵，对老兵的伤害加倍。',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.baiBuChuanYang],
        isInit: false,
    },
    // 水雷战车
    shuiLeiZhanChe: {
        type: 'shuiLeiZhanChe',
        name: '水雷战车',
        attack: 75,
        defense: 12,
        hp: 520,
        addattack: 75,
        adddefense: 12,
        addhp: 20,
        desc: '此兵种可以克制火炮战车，对火炮战车的伤害加倍。',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.xuanGuangShuiLei],
        isInit: false,
    },
    // 火炮战车
    huoPaoZhanChe: {
        type: 'huoPaoZhanChe',
        name: '火炮战车',
        attack: 70,
        defense: 10,
        hp: 520,
        addattack: 70,
        adddefense: 10,
        addhp: 20,
        desc: '此兵种可以克制烈火步兵，对烈火步兵的伤害加倍。',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.zhanChe],
        isInit: false,
    },
    // 云游和尚
    yunYouHeShang: {
        type: 'yunYouHeShang',
        name: '云游和尚',
        attack: 75,
        defense: 13,
        hp: 520,
        addattack: 75,
        adddefense: 13,
        addhp: 20,
        desc: '此兵种可以克制三公子，对三公子的伤害加倍',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.eMiTuoFo],
        isInit: false,
    },
    // 三公子
    sanGongZi: {
        type: 'sanGongZi',
        name: '三公子',
        attack: 60,
        defense: 20,
        hp: 1560,
        addattack: 60,
        adddefense: 20,
        addhp: 20,
        desc: '',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.feiXianJianFa],
        isInit: false,
    },
    // 云游道长
    yunYouDaoZhang: {
        type: 'yunYouDaoZhang',
        name: '云游道长',
        attack: 77,
        defense: 12,
        hp: 572,
        addattack: 77,
        adddefense: 12,
        addhp: 20,
        desc: '',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.tiTianSuanMing],
        isInit: false,
    },
    // 女真兵
    nvZhenBing: {
        type: 'nvZhenBing',
        name: '女真兵',
        attack: 80,
        defense: 20,
        hp: 520,
        addattack: 80,
        adddefense: 20,
        addhp: 20,
        desc: '',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.suXinJianFa],
        isInit: false,
    },
    // 冲锋战车
    chongFengZhanChe: {
        type: 'chongFengZhanChe',
        name: '冲锋战车',
        attack: 80,
        defense: 10,
        hp: 520,
        addattack: 80,
        adddefense: 10,
        addhp: 20,
        desc: '此兵种可以克制水雷战车，对水雷战车的伤害加倍。',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.zhanPao],
        isInit: false,
    },
    // 神鞭手
    shenBianShou: {
        type: 'shenBianShou',
        name: '神鞭手',
        attack: 75,
        defense: 50,
        hp: 990,
        addattack: 75,
        adddefense: 50,
        addhp: 20,
        desc: '',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.shenBian],
        isInit: false,
    },
    // 双枪猎手
    shuangQiangLieShou: {
        type: 'shuangQiangLieShou',
        name: '双枪猎手',
        attack: 50,
        defense: 80,
        hp: 800,
        addattack: 50,
        adddefense: 80,
        addhp: 20,
        desc: '',
        recruitFood: 250,
        recruitGold: 300,
        recruitTime: 600,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.shuangQiang],
        isInit: false,
    },
    // 五虎棍兵
    wuHuGunBing: {
        type: 'wuHuGunBing',
        name: '五虎棍兵',
        attack: 31,
        defense: 198,
        hp: 928,
        addattack: 31,
        adddefense: 198,
        addhp: 20,
        desc: '此兵种可以克制超水兵，对超水兵的伤害加倍。',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.luanYanGunFa, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 五虎锤手
    wuHuChuiShou: {
        type: 'wuHuChuiShou',
        name: '五虎锤手',
        attack: 30,
        defense: 70,
        hp: 3388,
        addattack: 30,
        adddefense: 70,
        addhp: 20,
        desc: '此兵种可以克制龙魄兵，对龙魄兵的伤害加倍。',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.jinGuangCiYanChui, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 五虎剑客
    wuHuJianKe: {
        type: 'wuHuJianKe',
        name: '五虎剑客',
        attack: 90,
        defense: 50,
        hp: 520,
        addattack: 90,
        adddefense: 50,
        addhp: 20,
        desc: '',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.duBiJianFa],
        isInit: false,
    },
    // 五虎刀客
    wuHuDaoKe: {
        type: 'wuHuDaoKe',
        name: '五虎刀客',
        attack: 36,
        defense: 60,
        hp: 828,
        addattack: 36,
        adddefense: 60,
        addhp: 20,
        desc: '此兵种可以克制三公子，对三公子的伤害加倍。',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.suiLangDaoFa],
        isInit: false,
    },
    // 五虎骑兵
    wuHuQiBing: {
        type: 'wuHuQiBing',
        name: '五虎骑兵',
        attack: 48,
        defense: 64,
        hp: 1014,
        addattack: 48,
        adddefense: 64,
        addhp: 20,
        desc: '此兵种可以克制黑金锤手，对黑金锤手的伤害加倍。',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.chiYunQiangFa, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 五虎板斧重兵
    wuHuBanFuZhongBing: {
        type: 'wuHuBanFuZhongBing',
        name: '五虎板斧重兵',
        attack: 22,
        defense: 140,
        hp: 1133,
        addattack: 22,
        adddefense: 140,
        addhp: 20,
        desc: '此兵种可以克制禁军侍卫，对禁军侍卫的伤害加倍。 ',
        recruitFood: 750,
        recruitGold: 750,
        recruitTime: 450,
        population: 4,
        skill: [skillTemplate.puTongGongJi, skillTemplate.hengSaoQianJun, skillTemplate.kuaiGong],
        isInit: false,
    },
    // 背嵬军
    beiWeiJun: {
        type: 'beiWeiJun',
        name: '背嵬军',
        attack: 80,
        defense: 80,
        hp: 3500,
        addattack: 80,
        adddefense: 80,
        addhp: 20,
        desc: '背嵬军是岳飞统领的一支精锐部队。',
        recruitFood: 500,
        recruitGold: 500,
        recruitTime: 500,
        population: 2,
        skill: [skillTemplate.puTongGongJi, skillTemplate.chongFengQiangFa, skillTemplate.pinMingYiJi],
        isInit: false,
    },
};
//武将模板
const generalTemplate = {
    //周武师
    zhouwushi: {
        name: '周武师',
        skill: [skillTemplate.puTongGongJi, skillTemplate.daDaoKanShu, skillTemplate.naHan],
        soldier: [soldierTemplate.bubing],
    },
    //史进
    shijin: {
        name: '史进',
        skill: [skillTemplate.puTongGongJi, skillTemplate.yanLingDaoFa], // Go数据中史进只有两个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.daoFuShou],
    },
    //王进
    wangjin: {
        name: '王进',
        skill: [skillTemplate.puTongGongJi, skillTemplate.luanGun], // Go数据中王进只有两个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.luanGongJun],
    },
    // 朱武
    zhuwu: {
        name: '朱武',
        skill: [skillTemplate.puTongGongJi, skillTemplate.wuShengDaoFa],
        soldier: [soldierTemplate.bubing, soldierTemplate.ciQiangBing], // ID "4" -> 刺枪兵
    },
    // 刘玉心
    liuyuxin: {
        name: '刘玉心',
        skill: [skillTemplate.puTongGongJi, skillTemplate.yuXinJianFa],
        soldier: [soldierTemplate.bubing, soldierTemplate.jinjianvbing], // ID "7" -> 金甲女兵
    },
    // 李忠
    lizhong: {
        name: '李忠',
        skill: [skillTemplate.puTongGongJi, skillTemplate.daHuGunFa],
        soldier: [soldierTemplate.bubing, soldierTemplate.kuaiDaoShou], // ID "5" -> 快刀手
    },
    // 周通
    zhoutong: {
        name: '周通',
        skill: [skillTemplate.puTongGongJi, skillTemplate.baWangJianFa],
        soldier: [soldierTemplate.bubing, soldierTemplate.kuaiJianShou], // ID "6" -> 快剑手
    },
    // 鲁智深
    luzhishen: {
        name: '鲁智深',
        skill: [skillTemplate.puTongGongJi, skillTemplate.xiaoXiaoLuoMuDaoFa, skillTemplate.daLiJinGangGunFa], // 对应 Go 数据中的两个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.daLiGunShou, soldierTemplate.daLiDaoShou], // ID "8" -> 大力棍手, ID "9" -> 大力刀手
    },
    // 柴进
    chaijin: {
        name: '柴进',
        skill: [skillTemplate.puTongGongJi, skillTemplate.chaiJiaQiangFa],
        soldier: [soldierTemplate.bubing, soldierTemplate.chongFengQiangBing], // ID "10" -> 冲锋枪兵
    },
    // 林冲
    linchong: {
        name: '林冲',
        skill: [skillTemplate.puTongGongJi, skillTemplate.chuiYunPoFengQiangFa],
        soldier: [soldierTemplate.bubing, soldierTemplate.zhongJianShou], // ID "11" -> 重剑手
    },
    // 刘唐
    liutang: {
        name: '刘唐',
        skill: [skillTemplate.puTongGongJi, skillTemplate.chiMuDaoFa],
        soldier: [soldierTemplate.bubing, soldierTemplate.kuangBaoBuBing], // ID "12" -> 狂暴步兵
    },
    // 阮小二
    ruanxiaoer: {
        name: '阮小二',
        skill: [skillTemplate.puTongGongJi, skillTemplate.ruoShuiDaoFa],
        soldier: [soldierTemplate.bubing, soldierTemplate.shuiBing], // ID "15" -> 水兵
    },
    // 杨志
    yangzhi: {
        name: '杨志',
        skill: [skillTemplate.puTongGongJi, skillTemplate.yangJiaWuHeQiang],
        soldier: [soldierTemplate.bubing, soldierTemplate.tieJiaQiangBing], // ID "13" -> 铁甲枪兵
    },
    // 公孙胜
    gongsunSheng: {
        name: '公孙胜',
        skill: [skillTemplate.puTongGongJi, skillTemplate.maoShanDaoJian],
        soldier: [soldierTemplate.bubing, soldierTemplate.muOuBing], // ID "14" -> 木偶兵
    },
    // 吴用
    wuyong: {
        name: '吴用',
        skill: [skillTemplate.puTongGongJi, skillTemplate.tianJiJianFa],
        soldier: [soldierTemplate.bubing, soldierTemplate.shenXingGunBing], // ID "16" -> 神行棍兵
    },
    // 武松
    wusong: {
        name: '武松',
        skill: [skillTemplate.puTongGongJi, skillTemplate.zuiDao],
        soldier: [soldierTemplate.bubing, soldierTemplate.changBiDaoBing], // ID "17" -> 长臂刀兵
    },
    // 李逵
    likui: {
        name: '李逵',
        skill: [skillTemplate.puTongGongJi, skillTemplate.daLiBanFu],
        soldier: [soldierTemplate.bubing, soldierTemplate.daLiFuTouBing], // ID "18" -> 大力斧头兵
    },
    // 扈三娘
    husanniang: {
        name: '扈三娘',
        skill: [skillTemplate.puTongGongJi, skillTemplate.zhuFuJianFa, skillTemplate.zhuFuDaoFa],
        soldier: [soldierTemplate.bubing], // 只有步兵
    },
    // 小雨
    xiaoyu: {
        name: '小雨',
        skill: [skillTemplate.puTongGongJi], // Go数据中小雨只有一个技能
        soldier: [soldierTemplate.bubing],
    },
    // 徐宁
    xuning: {
        name: '徐宁',
        skill: [skillTemplate.puTongGongJi], // Go数据中徐宁只有一个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.huangJinQiangBing, soldierTemplate.chongFengZhanChe], // ID "116" -> 黄金枪兵, ID "211" -> 冲锋战车
    },
    // 杨春
    yangchun: {
        name: '杨春',
        skill: [skillTemplate.puTongGongJi], // Go数据中杨春只有一个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.huangJinQiangBing, soldierTemplate.yunYouDaoZhang], // ID "116" -> 黄金枪兵, ID "209" -> 云游道长
    },
    // 陈达
    chend: {
        name: '陈达',
        skill: [skillTemplate.puTongGongJi], // Go数据中陈达只有一个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.huangJinQiangBing, soldierTemplate.yunYouHeShang], // ID "116" -> 黄金枪兵, ID "207" -> 云游和尚
    },
    // 关胜
    guansheng: {
        name: '关胜',
        skill: [skillTemplate.puTongGongJi], // Go数据中关胜只有一个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.xianBing, soldierTemplate.yunEr], // ID "119" -> 仙兵, ID "201" -> 云儿
    },
    // 阮小七
    ruanxiaoqi: {
        name: '阮小七',
        skill: [skillTemplate.puTongGongJi], // Go数据中阮小七只有一个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.chaoShuiBing], // ID "134" -> 超水兵
    },
    // 阮小五
    ruanxiaowu: {
        name: '阮小五',
        skill: [skillTemplate.puTongGongJi], // Go数据中阮小五只有一个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.chaoShuiBing], // ID "134" -> 超水兵
    },
    // 董平
    dongping: {
        name: '董平',
        skill: [skillTemplate.puTongGongJi], // Go数据中董平只有一个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.shuangQiangLieShou], // ID "213" -> 双枪猎手
    },
    // 秦明
    qinming: {
        name: '秦明',
        skill: [skillTemplate.puTongGongJi], // Go数据中秦明只有一个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.huangJinQiangBing], // ID "116" -> 黄金枪兵
    },
    // 呼延灼
    huyanZhuo: {
        name: '呼延灼',
        skill: [skillTemplate.puTongGongJi], // Go数据中呼延灼只有一个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.shenBianShou], // ID "212" -> 神鞭手
    },
    // 卢俊义
    lujunyi: {
        name: '卢俊义',
        skill: [skillTemplate.puTongGongJi], // Go数据中卢俊义只有一个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.yunEr, soldierTemplate.yanQing], // ID "201" -> 云儿, ID "204" -> 燕青
    },
    // 宋江
    songjiang: {
        name: '宋江',
        skill: [skillTemplate.puTongGongJi], // Go数据中宋江只有一个技能
        soldier: [soldierTemplate.lieHuoBuBing, soldierTemplate.beiWeiJun, soldierTemplate.longPoBing], // ID "135" -> 烈火步兵, ID "227" -> 背嵬军, ID "121" -> 龙魄兵
    },
    // 战狼
    zhanlang: {
        name: '战狼',
        skill: [skillTemplate.puTongGongJi], // Go数据中战狼只有一个技能
        soldier: [soldierTemplate.bubing, soldierTemplate.laoBing, soldierTemplate.shuiLeiZhanChe], // ID "203" -> 老兵, ID "205" -> 水雷战车
    },
    // 宝宝
    baobao: {
        name: '宝宝',
        skill: [skillTemplate.puTongGongJi, skillTemplate.zhuFuJianFa, skillTemplate.zhuFuDaoFa],
        soldier: [soldierTemplate.bubing],
    },
    // 养子
    yangzi: {
        name: '养子',
        skill: [skillTemplate.puTongGongJi, skillTemplate.zhuFuJianFa, skillTemplate.zhuFuDaoFa],
        soldier: [soldierTemplate.bubing],
    },
    // 养女
    yangnv: {
        name: '养女',
        skill: [skillTemplate.puTongGongJi, skillTemplate.zhuFuJianFa, skillTemplate.zhuFuDaoFa],
        soldier: [soldierTemplate.bubing, soldierTemplate.nvZhenBing], // ID "210" -> 女真兵
    },
    // 二宝宝
    erbaobao: {
        name: '二宝宝',
        skill: [skillTemplate.puTongGongJi, skillTemplate.zhuFuJianFa, skillTemplate.zhuFuDaoFa],
        soldier: [soldierTemplate.bubing],
    },
};
//传入士兵数组 返回 原始兵种和学习兵种数量
function getSoldierNum(soldierList:any[]){
    let originalSoldierNum=0
    let learnSoldierNum=0
    soldierList.forEach(item=>{
        soldierTemplate[item.type].isInit?originalSoldierNum+=1:learnSoldierNum+=1
    })
    return {originalSoldierNum,learnSoldierNum}
}


//是否纯数字
function isPureNumber(str) {
    return /^\d+$/.test(str);
}
//返回物品类型
function getGoodType(type, subType) {
    let str = '',
        typeArr = {
            1: '消耗品', 2: '兵器', 3: '防具', 4: '士兵兵器', 5: '士兵防具', 6: '坐骑装备', 7: '装备宝石', 8: '其它'
        },
        subTypeArr = {
            21: '刀', 22: '剑', 23: '棍', 24: '斧', 25: '锤', 26: '枪',
            31: '帽子', 32: '衣服', 33: '裤子', 34: '鞋子', 35: '项链', 36: '戒指', 37: '手套', 38: '肩甲', 39: '披风', 40: '特殊戒指', 41: '左手套', 42: 'PK盾',
            61: '蹄铁', 62: '马鞍', 63: '缰绳', 64: '马铠', 65: '马蹬', 66: '马嚼',
            71: '商城', 72: '强化', 73: '材料', 74: '训练', 75: '任务', 76: '活动', 77: '神兵', 78: '其它'
        };
    str += typeArr[type];
    subType && (str += '·' + subTypeArr[subType])
    return str
}
//根据等级计算初始血量
function getHpByLevel(level) {
    const fnExp = () => {
        let sum = 0, a = 1;
        for (let i = 1; i <= level; i++) {
            sum += a;
            a += 2;
        }
        return sum
    }
    return 2600 + level * 100 + fnExp()
}
// 角色升级所需资源计算公式 时间返回为秒
function roleUpgrade(start: number, end = start + 1) {
    const fnExp = (to) => {
        let a = 100;
        let result = 0
        for (let i = 2; i <= to; i++) {
            a += i * 200 - 100
            result += a;
        }
        return result
    }
    let resource = fnExp(end) - fnExp(start)
    let { time, pot } = roleUpgrade1(start, end)
    // let potential=time*
    return { resource, time, pot }
}
//传入资源返回最大升级多少
function checkMaxLevel(start: number, resource1: number, pot1: number) {
    let end = start
    for (let index = 1; index < 2000; index++) {
        end=start+index
        let { resource, pot } = roleUpgrade(start, end)
        if (resource1 < resource || pot1 < pot) {
            let { resource, pot } = roleUpgrade(start, end - 1)
            return { end, resource, pot }
        }
    }

}
// 角色升级所需时间计算公式
function roleUpgrade1(start: number, end = start + 1) {
    const fnExp = (to: number) => {
        let a = -2;
        let result = 0
        for (let i = 2; i <= to; i++) {
            a += (i * 4)
            result += a;
        }
        return result
    }
    const fn = (to: number) => {
        let result = 0
        let pot = 0
        for (let i = 1; i <= to; i++) {
            let a = fnExp(i)
            result += a
            pot += (Math.ceil(i / 2) * a)
        }
        return { time: result, pot }
    }
    let { time: time1, pot: pot1 } = fn(start)
    let { time: time2, pot: pot2 } = fn(end)
    let result = time2 - time1
    let pot = pot2 - pot1
    return { time: result, pot }
}
//房屋和人口计算
function manorPopulation(level) {
    return 10 + level* 20
}
//经脉处理
//冲脉、带脉、阴维脉、阳维脉、阴跷脉、阳跷脉、任脉、督脉
/**
 * 
 * @param name 已开脉名称
 * @returns 已开脉字符串和未开脉对象
 */
function jingmaiFn(name) {
    //粮草和潜能使用一样多
    let ziyuan: any = {
        '冲脉': { potential: 12100000, longdan: 1 },
        '带脉': { potential: 12100000, longdan: 2 },
        '阴维脉': { potential: 12100000, longdan: 4 },
        '阳维脉': { potential: 12100000, longdan: 6 },
        '阴跷脉': { potential: 12100000, longdan: 8 },
        '阳跷脉': { potential: 12100000, longdan: 10 },
        '任脉': { potential: 12100000, longdan: 15 },
        '督脉': { potential: 12100000, longdan: 30 },
    }
    let str = '', obj: any = {}
    let markBool = false;
    for (let key in ziyuan) {
        if (markBool || !name) {
            obj = JSON.parse(JSON.stringify(ziyuan[key]))
            obj.value = `打通${key}需要龙灵仙凤丹x${ziyuan[key].longdan}、粮食x${ziyuan[key].potential}、潜能x${ziyuan[key].potential}，你确定要打通吗？`
            obj.name = key
            obj.longdanNum = ziyuan[key].longdan
            obj.populationNum = ziyuan[key].potential
            break;
        }
        if (key == name) {
            markBool = true
            str += key
        }
    }
    return { opened: str || '无', notOpen: obj }
}
//开启魂魄 金木水火土
function hunpofn(name) {
    let ziyuan = {
        '金': { potential: 133100000, hunpoNum: 10, hunpoId: 41 },
        '木': { potential: 172800000, hunpoNum: 12, hunpoId: 42 },
        '水': { potential: 219700000, hunpoNum: 14, hunpoId: 43 },
        '火': { potential: 274400000, hunpoNum: 16, hunpoId: 44 },
        '土': { potential: 337500000, hunpoNum: 20, hunpoId: 45 },
    }
    let str = '', obj: any = {}
    let markBool = false;
    for (let key in ziyuan) {
        if (markBool || !name) {
            obj = JSON.parse(JSON.stringify(ziyuan[key]))
            obj.value = `打通魂魄[${key}]需要魂魄灵珠[${key}]x10、潜能x${ziyuan[key].potential}、粮食x${ziyuan[key].potential}、木材x${ziyuan[key].potential}、生铁x${ziyuan[key].potential}、石料x${ziyuan[key].potential}，你确定要打通吗？`
            obj.name = key//名称
            obj.hunpoNum = ziyuan[key].hunpoNum//魂魄数量
            obj.hunpoId = ziyuan[key].hunpoId//魂魄id
            obj.populationNum = ziyuan[key].potential//资源
            break;
        }
        if (key == name) {
            markBool = true
            str += key
        }
    }
    return { opened: str || '无', notOpen: obj }
}
const handelfn:any={}
handelfn.jingmai=(name:string)=>{
    let ziyuan: any = {
        '冲脉': { gongji: 0.05, fangyu: 0.01 },
        '带脉': { gongji: 0.1, fangyu: 0.02 },
        '阴维脉': { gongji: 0.16, fangyu: 0.03 },
        '阳维脉': { gongji: 0.22, fangyu: 0.04 },
        '阴跷脉': { gongji: 0.28, fangyu: 0.05 },
        '阳跷脉': { gongji: 0.36, fangyu: 0.07 },
        '任脉': { gongji: 0.50, fangyu: 0.09 },
        '督脉': { gongji: 0.69, fangyu: 0.11 },
    };
    return ziyuan[name]
}
handelfn.hunpo=(name:string)=>{
    let ziyuan: any = {
        '金': { gongji: 0.05, fangyu: 0.01 },
        '木': { gongji: 0.1, fangyu: 0.03 },
        '水': { gongji: 0.16, fangyu: 0.06 },
        '火': { gongji: 0.22, fangyu: 0.09 },
        '土': { gongji: 0.28, fangyu: 0.13 },
    }
    return ziyuan[name]
}
export {
    handelfn,
    hunpofn,
    jingmaiFn,
    buildType,
    manorPopulation,
    handlbuildfn,
    getHpByLevel,
    roleUpgrade,
    checkMaxLevel,
    extractBetween,
    goodsType,
    weaponType,
    formatDate,
    defaultConfig,
    filterRouter,
    buildconfig,
    formatDuring,
    manorEventfn,
    generalTemplate,
    isPureNumber,
    getGoodType,
    soldierTemplate,
    getRandomInt,
    getRandomNumber,
    equipmentMountType,
    goodsType1,
    getSoldierNum,
    equipmentType
}