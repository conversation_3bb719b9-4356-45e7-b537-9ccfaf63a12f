import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { GeneralEntity } from 'src/entities/general.entity';
import { BlessEntity } from 'src/entities/general/bless.entity';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';
import { SoldierEntity } from 'src/entities/soldier.entity';
import { CommonService } from 'src/middleware/common.service';
import { DataSource, EntityManager, Equal, Repository, Column } from 'typeorm';
import { GcmdService } from '../gcmd.service';
import * as taskJsonwodezhuangyuan from '../../taskJson/main.json'
import * as taskJsonshijiazhuang from '../../taskJson/main-shijiazhuang.json'
import * as taskJsonshaohuashan from '../../taskJson/main-shaohuashan.json'
import * as taskJsontaohuacun from '../../taskJson/main-taohuacun.json'
import * as taskJsontaohuashan from '../../taskJson/main-taohuashan.json'
import * as taskJsondongjing from '../../taskJson/main-dongjing.json'
import * as taskJsonchaijiazhuanghecangzhou from '../../taskJson/main-chaijiazhuanghecangzhou.json'
import * as branchLineJson from '../../taskJson/branchLine.json'
import { RedisService } from 'src/middleware/redis.service';
import { RoleTasksEntity } from 'src/entities/roleTasks.entity';
import { generalEquip, taskListConfig } from 'src/utils/config1';
import { GeneralService } from '../general.service';
import { RoleEntity } from 'src/entities/role.entity';
import { getHpByLevel, getSoldierNum, soldierTemplate } from 'src/utils/config';
import { SkillEntity } from 'src/entities/skill.entity';
@Injectable()
export class OtherService {
    private readonly serviceName = 'general'
    private readonly size = 20
    private nearBygeneralNum = 4;//近身武将数量
    constructor(
        private readonly commonService: CommonService,
        private eventEmitter: EventEmitter2,
        private redisService: RedisService,

        @Inject(forwardRef(() => GcmdService))private readonly gcmdService: GcmdService,
        @Inject(forwardRef(() => GeneralService))private readonly generalService: GeneralService,

        @InjectRepository(GeneralEntity) private readonly generalEntity: Repository<GeneralEntity>,
        @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
        @InjectRepository(SoldierEntity) private readonly soldierEntity: Repository<SoldierEntity>,
        @InjectRepository(BlessEntity) private readonly blessEntity: Repository<BlessEntity>,
        @InjectRepository(PersonGoodsEntity) private readonly personGoodsEntity: Repository<PersonGoodsEntity>,
        @InjectRepository(RoleTasksEntity) private readonly roleTasksEntity: Repository<RoleTasksEntity>,
        @InjectRepository(SkillEntity) private readonly skillEntity: Repository<SkillEntity>,
        private dataSource: DataSource,
    ) { }
    //武将士兵人物使用物品逻辑处理
    /**
     * 
     * @param type general,soldier
     * @param itemId 武将或士兵id
     * @param goodInfo 玩家物品关联 good
     */
    async useGoodsLogic(type, itemId, goodInfo,goodNum=1) {
        let obj={code:'error',msg:'使用物品失败了<br/>'};
        let msg='使用物品失败了<br/>'
        if(!goodInfo||goodInfo.count<1){
            msg='物品数量不足'
            return {
                code:'error',msg
            }
        }
        let itemInfo:any; 
        if(type=='user'){
            itemInfo=await this.roleEntity.findOne({where:{id:itemId}})
            await this.dataSource.transaction(async (manager) => {
                obj=await this.allAddGood(goodInfo,itemInfo,manager,RoleEntity,itemId,type,goodNum)
                if(obj.code=='ok'){
                    if(goodInfo.count>1){
                        goodInfo.count--
                        await manager.save(goodInfo) 
                    }else{
                        await manager.remove(goodInfo)
                    }
                }
            })
            await this.generalService.calculatePower(type,itemId)
        }else{
            if(type=='general'){
                itemInfo=await this.generalEntity.findOne({where:{id:itemId},relations:['soldiers']})
            }
            if(type=='soldier'){
                itemInfo=await this.soldierEntity.findOne({where:{id:itemId}})
            }
            await this.dataSource.transaction(async (manager) => {
                let attackArr=['狂暴一','狂暴二','狂暴三','龙魄狂暴','狂暴奇书','极度狂暴奇书','龙魄狂暴奇书']
                let defenseArr=['金刚罩一','金刚罩二','修神卷轴','龙魄金刚','金刚奇书','血印金刚奇书','龙魄金刚奇书']
                let allArr=['高级魄力丹','魄力丹','攻击秘术','防御秘术','生命储备丹','VIP月卡','四倍潜能卡','双倍潜能卡','双倍银两卡','四倍资源卡','双倍资源卡']
                let hpArr=['万虫羹','高级万虫羹','顶级万虫羹','强体奇书一','强体奇书二','强体奇书三']
                if(attackArr.includes(goodInfo.good.name)){
                    obj=await this.attackAddGood(goodInfo,itemInfo,manager,GeneralEntity,itemId,type)
                }else if(defenseArr.includes(goodInfo.good.name)){
                    obj=await this.defenseAddGood(goodInfo,itemInfo,manager,GeneralEntity,itemId,type)
                }else if(hpArr.includes(goodInfo.good.name)){
                    obj=await this.hpAddGood(goodInfo,itemInfo,manager,GeneralEntity,itemId,type)
                }else{
                    obj=await this.allAddGood(goodInfo,itemInfo,manager,GeneralEntity,itemId,type,goodNum)
                }
                if(obj.code=='ok'){
                    if(goodInfo.count>1){
                        goodInfo.count--
                        await manager.save(goodInfo) 
                    }else{
                        await manager.remove(goodInfo)
                    }
                }
            })
            //计算战力
            await this.generalService.calculatePower(type,itemId)
        }
        return obj
    }
    //获取任务列表 并返回可以执行的任务
    async getTaskList(userId:number,gpsId=null,npcId=null){

        const returnObj={type:2,content:''}
        let target=gpsId?`map-${gpsId}`:`npc-${npcId}`
        const userTaskList=await this.getUserTaskList(userId)
        const mainTask=userTaskList.find(item=>item.taskId==1)
        for(let i=0;i<taskListConfig.length;i++){
            let taskInfo=taskListConfig[i]
            let userTask=userTaskList.find(item=>item.taskId==taskInfo.id)
            if(userTask){
                let taskJson=this.getTaskListByTaskId(taskInfo.id,userTask.stepId)
                let nextStep=userTask.stepId+1;
                let nextIndex=userTask.stepId+1-taskJson.initIndex;
                let previnfo=taskJson.steps[nextIndex-1]
                if(taskJson.steps.length==nextIndex){
                    if(taskInfo.id==1){
                        nextStep=Math.ceil(nextStep/1000) * 1000
                        taskJson=this.getTaskListByTaskId(taskInfo.id,nextStep)
                        nextIndex=0
                        if(!taskJson){
                            return returnObj
                        }
                    }else{
                        return returnObj
                    }
                }
                
                let nextinfo=taskJson.steps[nextIndex]
                if(target!=nextinfo.condition.release){
                    if(target==previnfo.condition.release){
                        returnObj.content=previnfo.desc.replace(/(?:银两|潜能|粮草|木材|石料|生铁)\+\d+<br\/>/g, '');
                        returnObj.content=`[${taskJson.title}]</br>${returnObj.content}</br>======================</br>`
                    }
                    return returnObj
                }
                let obj=await this.gcmdService.checkTaskStatus(previnfo.condition, Number(userId))
                if(!obj.bool){
                    returnObj.content=previnfo.desc.replace(/(?:银两|潜能|粮草|木材|石料|生铁)\+\d+<br\/>/g, '');
                    returnObj.type=2
                    returnObj.content=`[${taskJson.title}]</br>${returnObj.content}任务：${obj.msg}</br>======================</br>`
                    return returnObj
                }
                if(previnfo&&nextinfo){
                    if(nextinfo.desc.includes('成为你的武将')){
                        const regex = /“(.*?)“/; // This regular expression looks for any characters between “ and “
                        const match = nextinfo.desc.match(regex);
    
                        if (match && match[1]) {
                            this.generalService.addGeneralAll(String(userId),match[1])
                        }
                    }
                    await this.gcmdService.removeTaskGood(Number(userId),previnfo.condition)
                    await this.roleTasksEntity.update({id:userTask.id},{stepId:nextStep})
                    await this.redisService.hdel('user' + userId, 'taskList')
                    await this.gcmdService.giveAward(Number(userId), taskInfo.id+'-'+userTask.stepId,nextinfo.taskReward)
                    returnObj.content=nextinfo.desc
                    returnObj.type=1
                    return returnObj
                }
            }else{
                //未接的任务
                if(target==taskInfo.condition.release){
                    //存在要求主线任务的条件
                    if(taskInfo.condition.mainTaskId&&mainTask.stepId>=taskInfo.condition.mainTaskId){
                        const taskJson=branchLineJson.tasks.find(item=>item.id==taskInfo.id)
                        await this.roleTasksEntity.save({role:{id:userId},taskId:taskInfo.id,stepId:0,taskName:taskJson.title})
                        await this.redisService.hdel('user' + userId, 'taskList')
                        return {type:1,content:taskJson.steps[0].desc}
                    }else{
                        if(taskInfo.id==1){
                            await this.roleTasksEntity.save({role:{id:userId},taskId:taskInfo.id,stepId:0,taskName:taskJsonwodezhuangyuan.title})
                            await this.redisService.hdel('user' + userId, 'taskList')
                            return {type:1,content:taskJsonwodezhuangyuan.steps[0].desc}
                        }
                    }
                }
            }
        }
        return returnObj
    }
    //根据任务id 返回任务列表
    getTaskListByTaskId(taskId:number,stepId:number){
        let copyId=stepId;
        if(taskId==1){
            let taskJson=null
            if(stepId>=0&&stepId<1000){
                taskJson=taskJsonwodezhuangyuan
            }else if(stepId>=1000&&stepId<2000){
                stepId=stepId-1000
                taskJson=taskJsonshijiazhuang
            }else if(stepId>=2000&&stepId<3000){
                stepId=stepId-2000
                taskJson=taskJsonshaohuashan
            }else if(stepId>=3000&&stepId<4000){
                stepId=stepId-3000
                taskJson=taskJsontaohuacun
            }else if(stepId>=4000&&stepId<5000){
                stepId=stepId-4000
                taskJson=taskJsontaohuashan
            }else if(stepId>=5000&&stepId<6000){
                stepId=stepId-5000
                taskJson=taskJsondongjing
            }else if(stepId>=6000&&stepId<7000){
                stepId=stepId-6000
                taskJson=taskJsonchaijiazhuanghecangzhou
            }
            // if(stepId==(taskJson.steps.length-1)){
            //     stepId=Math.ceil(copyId/1000) * 1000
            //     return this.getTaskListByTaskId(taskId,stepId)
            // }
            return taskJson
        }else{
            const taskJson=branchLineJson.tasks.find(item=>item.id==taskId)
            return taskJson
        }
        
    }

    async getUserTaskList(userId: number) {
        let taskList=await this.redisService.hget('user'+userId, 'taskList')
        if(taskList==null||taskList=='null'){
            let taskList = await this.roleTasksEntity.find({ where: {role:{id: userId}} })
            taskList&&(await this.redisService.hset('user'+userId, 'taskList', JSON.stringify(taskList)))
            return taskList
        }else{
            return JSON.parse(taskList)
        }
    }
    //传入武将/士兵信息，返回攻击、防御、血量
    async addEquip(generalInfo:any){
        let attack1 = 0, defense1 = 0, hp1 = 0;
        for (const equip in generalEquip) {
            let item=generalEquip[equip]
            if(generalInfo[item.id]){
                let goodInfo=await this.personGoodsEntity.findOne({where:{id:generalInfo[item.id]}})
                if(goodInfo?.attack)attack1 += goodInfo.attack
                if(goodInfo?.defense)defense1 += goodInfo.defense 
                
            }
            
        }
        return { attack1, defense1, hp1 }
    }
    //攻击强化类物品处理-武将、士兵使用
    async attackAddGood(goodInfo:any,itemInfo:any,manager:EntityManager,entity:any,itemId:number,type:string){
        //狂暴一、狂暴二、狂暴三、龙魄狂暴
        //狂暴奇书、极度狂暴奇书、龙魄狂暴奇书
        let msg=''
        if(['狂暴一','狂暴二','狂暴三','龙魄狂暴'].includes(goodInfo.good.name)){
            let {attack1}=await this.addEquip(itemInfo)
            let maxAttack=0.5
            goodInfo.good.name=='狂暴二'&&(maxAttack=0.75)
            goodInfo.good.name=='狂暴三'&&(maxAttack=1)
            goodInfo.good.name=='龙魄狂暴'&&(maxAttack=1.2)

            if(itemInfo.attackAdd>=(itemInfo.level+attack1)*10*maxAttack){
                msg=`使用${goodInfo.good.name}失败，最大不能超过原始攻击的百分之${maxAttack*100}%</br>`
                return {code:'error',msg}
            }else{
                let attackAdd=Math.ceil((itemInfo.level+attack1)*10*0.05);
                await manager.update(entity, { id: itemId }, { attackAdd: () => `attackAdd + ${attackAdd}` })
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功，增加'+attackAdd+'点攻击力</br>'
            }
        }
        switch (goodInfo.good.name) {
            case '狂暴奇书':
                if(itemInfo.raguar>=100){
                    msg='狂暴奇书使用失败，已拥有狂暴属性</br>'
                    return {code:'error',msg}
                }else{
                    await manager.update(entity, { id: itemId }, { raguar: () => `raguar + 10` })
                    msg=itemInfo.name+'使用'+goodInfo.good.name+'成功,当前进度百分之'+(itemInfo.raguar+10)+'</br>'
                }
                break;
            case '极度狂暴奇书':
                if(itemInfo.extremeRaguar>=100){
                    msg='极度狂暴奇书使用失败，已拥有极度狂暴属性</br>'
                    return {code:'error',msg}  
                }
                if(itemInfo.raguar<100){
                    msg='极度狂暴奇书使用失败，未拥有狂暴属性</br>'
                    return {code:'error',msg}
                }
                await manager.update(entity, { id: itemId }, {
                    extremeRaguar: () => `extremeRaguar + 10` 
                })
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功,当前进度百分之'+(itemInfo.extremeRaguar+10)+'</br>'
                break;
            case '龙魄狂暴奇书':
                if(itemInfo.longpoRaguar>=100){
                    msg='龙魄狂暴奇书使用失败，已拥有龙魄狂暴属性</br>'
                    return {code:'error',msg}
                }
                if(itemInfo.extremeRaguar<100){
                    msg='龙魄狂暴奇书使用失败，未拥有极度狂暴属性</br>'
                    return {code:'error',msg}
                }
                await manager.update(entity, { id: itemId }, {
                    longpoRaguar: () => `longpoRaguar + 10` 
                })
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功,当前进度百分之'+(itemInfo.longpoRaguar+10)+'</br>'
                break;
            default:
                break;
        }
        return {code:'ok',msg}
    }
    //防御强化类物品处理-武将、士兵使用
    async defenseAddGood(goodInfo:any,itemInfo:any,manager:EntityManager,entity:any,itemId:number,type:string){
        let msg=''
        if(['金刚罩一','金刚罩二','修神卷轴','龙魄金刚'].includes(goodInfo.good.name)){
            let {defense1}=await this.addEquip(itemInfo)
            let maxDefense=0.5
            goodInfo.good.name=='金刚罩二'&&(maxDefense=0.75)
            goodInfo.good.name=='修神卷轴'&&(maxDefense=1.2)
            goodInfo.good.name=='龙魄金刚'&&(maxDefense=1.5)
            if(itemInfo.defenseAdd>=(itemInfo.level+defense1/7)*maxDefense){
                msg=`使用${goodInfo.good.name}失败，最大不能超过原始防御的百分之${maxDefense*100}%</br>`
                return {code:'error',msg}
            }else{
                let defenseAdd=itemInfo.level+Math.round(defense1/7);
                await manager.update(entity, { id: itemId }, { defenseAdd: () => `defenseAdd + ${defenseAdd}` })
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功，增加'+defenseAdd+'点防御力</br>'
            }
        }
        switch (goodInfo.good.name) {
            case '金刚奇书':
                if(itemInfo.jingang>=100){
                    msg='金刚奇书使用失败，已拥有金刚属性</br>'
                    return {code:'error',msg}
                }else{
                    await manager.update(entity, { id: itemId }, { jingang: () => `jingang + 10` })
                    msg=itemInfo.name+'使用'+goodInfo.good.name+'成功,当前进度百分之'+(itemInfo.jingang+10)+'</br>'
                }
                break;
            case '血印金刚奇书':
                if(itemInfo.xueyinJingang>=100){
                    msg='血印金刚奇书使用失败，已拥有血印属性</br>'
                    return {code:'error',msg}
                }
                if(itemInfo.jingang<100){
                    msg='血印金刚奇书使用失败，未拥有金刚属性</br>'
                    return {code:'error',msg}
                }
                await manager.update(entity, { id: itemId }, {
                    xueyinJingang: () => `xueyinJingang + 10` 
                })
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功,当前进度百分之'+(itemInfo.xueyinJingang+10)+'</br>'
                break;
            case '龙魄金刚奇书':
                if(itemInfo.longpojingang>=100){
                    msg='龙魄金刚奇书使用失败，已拥有龙魄金刚属性</br>'
                    return {code:'error',msg}
                }
                if(itemInfo.xueyinJingang<100){
                    msg='龙魄金刚奇书使用失败，未拥有血印金刚属性</br>'
                    return {code:'error',msg}
                }
                await manager.update(entity, { id: itemId }, {
                    longpojingang: () => `longpojingang + 10` 
                })
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功,当前进度百分之'+(itemInfo.longpojingang+10)+'</br>'
                break;
        }
        return {code:'ok',msg}
    }
    //体力强化类物品处理-武将、士兵使用
    async hpAddGood(goodInfo:any,itemInfo:any,manager:EntityManager,entity:any,itemId:number,type:string){
        let msg=''
        if(['万虫羹','高级万虫羹','顶级万虫羹'].includes(goodInfo.good.name)){
            let {hp1}=await this.addEquip(itemInfo)
            let initHp=getHpByLevel(itemInfo.level)
            let maxHp=0.5
            goodInfo.good.name=='高级万虫羹'&&(maxHp=1)
            goodInfo.good.name=='顶级万虫羹'&&(maxHp=1.25)
            if(itemInfo.hpAdd>=(initHp+hp1)*maxHp){
                msg=`使用${goodInfo.good.name}失败，最大不能超过原始体力的百分之${maxHp*100}%</br>`
                return {code:'error',msg}
            }else{
                await manager.update(entity, { id: itemId }, { hpAdd: () => `hpAdd + 50` })
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功，增加50点体力</br>'
            }
        }
        switch (goodInfo.good.name) {
            case '强体奇书一':
                if(itemInfo.qiangti1>=100){
                    msg='强体奇书一使用失败，已拥有强体属性</br>'
                    return {code:'error',msg}
                }else{
                    await manager.update(entity, { id: itemId }, { qiangti1: () => `qiangti1 + 10` })
                    msg=itemInfo.name+'使用'+goodInfo.good.name+'成功,当前进度百分之'+(itemInfo.qiangti1+10)+'</br>'
                }
                break;
            case '强体奇书二':
                if(itemInfo.qiangti2>=100){
                    msg='强体奇书二使用失败，已拥有强体属性</br>'
                    return {code:'error',msg}
                }
                if(itemInfo.qiangti1<100){
                    msg='强体奇书二使用失败，未拥有强体奇书一属性</br>'
                    return {code:'error',msg}
                }
                await manager.update(entity, { id: itemId }, { qiangti2: () => `qiangti2 + 10` })
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功,当前进度百分之'+(itemInfo.qiangti2+10)+'</br>'
                break;
            case '强体奇书三':
                if(itemInfo.qiangti3>=100){
                    msg='强体奇书三使用失败，已拥有强体属性</br>'
                    return {code:'error',msg}
                }
                if(itemInfo.qiangti2<100){
                    msg='强体奇书三使用失败，未拥有强体奇书二属性</br>'
                    return {code:'error',msg}
                }
                await manager.update(entity, { id: itemId }, { qiangti3: () => `qiangti3 + 10` })
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功,当前进度百分之'+(itemInfo.qiangti3+10)+'</br>'
                break;
        }
        return {code:'ok',msg}
    }
    //综合类物品处理-武将、士兵使用
    async allAddGood(goodInfo:any,itemInfo:any,manager:EntityManager,entity:any,itemId:number,type:string,goodNum=1){
        let msg=''
        if(goodInfo.good.name=='魄力丹'){
            if(itemInfo.soul+goodNum>itemInfo.level){
                msg='魄力丹使用失败，不能超过当前等级</br>'
                return {code:'error',msg}
            }else{
                await manager.update(entity, { id: itemId }, { soul: () => `soul + ${goodNum}` })
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功，增加'+goodNum+'点魄力</br>'
            }
        }
        if(goodInfo.good.name=='高级魄力丹'){
            if(itemInfo.soul+goodNum>itemInfo.level+15){
                msg='高级魄力丹使用失败，魄力值不能超过等级15级</br>'
                return {code:'error',msg}
            }else{
                await manager.update(entity, { id: itemId }, { soul: () => `soul + ${goodNum}` })
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功，增加'+goodNum+'点魄力</br>'
            }
        }
        let goodList=['攻击秘术','防御秘术','生命储备丹','VIP月卡','四倍潜能卡','双倍潜能卡','双倍银两卡','四倍资源卡','双倍资源卡']
        if(goodList.includes(goodInfo.good.name)){
            let blessType=goodList.indexOf(goodInfo.good.name);
            let where=()=>{
                if(type=='general'){
                    return {generalId:Equal(itemId),blessType}
                }else if(type=='soldier'){
                    return {soldierId:Equal(itemId),blessType}
                }else{
                    return {userId:Equal(itemId),blessType}
                }
            }
            let blessInfo=await this.blessEntity.findOne({where:where()})
            if(blessInfo){
                if(goodInfo.good.name=='生命储备丹'){
                    if(blessInfo.endTime.getTime()+1000*60*60>new Date().getTime()){
                        msg='一小时内只能使用一个生命储备丹</br>'
                        return {code:'error',msg}
                    }
                    blessInfo.shengyu+=1200000
                    blessInfo.endTime=new Date()
                }else{
                    let initTime=1000*60*120//2小时
                    if(goodInfo.good.name=='VIP月卡'){
                        initTime=1000*60*60*24*30//30天
                    }
                    if(blessInfo.endTime.getTime()<new Date().getTime()){
                        blessInfo.endTime=new Date(Date.now()+initTime)
                    }else{
                        blessInfo.endTime=new Date(blessInfo.endTime.getTime()+initTime)
                    }
                }
                
            }else{
                blessInfo=new BlessEntity()
                if(type=='general'){
                    blessInfo.generalId=itemId
                }else if(type=='soldier'){
                    blessInfo.soldierId=itemId
                }else{
                    blessInfo.userId=itemId
                }
                blessInfo.blessType=blessType
                if(goodInfo.good.name=='生命储备丹'){
                    blessInfo.shengyu=1200000
                    blessInfo.endTime=new Date()
                }else{
                    let initTime=1000*60*120//2小时
                    if(goodInfo.good.name=='VIP月卡'){
                        initTime=1000*60*60*24*30//30天
                    }
                    blessInfo.endTime=new Date(Date.now()+initTime)
                }
            }
            blessInfo.status=1
            await manager.save(blessInfo)
            this.eventEmitter.emit('createBlessTimer', blessInfo)
            msg=itemInfo.name+'使用'+goodInfo.good.name+'成功,当前过期时间为'+(blessInfo.endTime.toLocaleString())+'</br>'
        }
        //兵书
        let xunlianshuList=["乱剑手训练术",
            "狂暴棍手训练术",
            "军斧兵训练术",
            "重甲枪兵训练术",
            "飞剑手训练术",
            "重锤手训练术",
            "神刀兵训练术",
            "敢死兵训练术",
            "飞刀手训练术",
            "金刚棍兵训练术",
            "陆行军训练术",
            "赤甲斧头兵训练术",
            "御林剑客训练术",
            "羽林刀客训练术",
            "黄金枪兵训练术",
            "禁军侍卫训练术",
            "千剑娘子军训练术",
            "天外飞仙(已成)",
            "上古神兵训练术",
            "龙魄兵训练术",
            "金锤手训练术",
            "黑金锤手训练术",
            "双节棍兵训练术",
            "三节棍兵训练术",
            "三枪骑兵训练术",
            "乱枪骑兵训练术",
            "板斧重兵训练术",
            "双斧重骑兵训练术",
            "单刀客训练术",
            "双刀客训练术",
            "独臂剑手训练术",
            "互博剑手训练术",
            "超水兵训练术",
            "烈火步兵训练术",
            "云儿训练术",
            "飞天云儿训练术",
            "老兵训练术",
            "燕青训练术",
            "水雷战车训练术",
            "火炮战车训练术",
            "云游和尚训练术",
            "三公子训练术",
            "云游道长训练术",
            "女真兵训练术",
            "冲锋战车训练术",
            "神鞭手训练术",
            "双枪猎手训练术",
            "五虎棍手训练术",
            "五虎锤手训练术",
            "五虎骑兵训练术",
            "五虎剑客训练术",
            "五虎刀客训练术",
            "五虎板斧重兵训练术",
        ]
        if(xunlianshuList.includes(goodInfo.good.name)){
            let item=itemInfo.soldiers.find(item=>goodInfo.good.name.includes(item.name))
            if(item){
                msg='训练术使用失败，已学习过该兵种</br>'
                return {code:'error',msg}
            }
            if(goodInfo.good.name=='天外飞仙(已成)'){
                let jinJunShiWei=itemInfo.soldiers.find(item=>item.type=='jinJunShiWei')
                if(!jinJunShiWei||jinJunShiWei.level<100){
                    msg='训练术使用失败，禁军侍卫等级未达到100级</br>'
                    return {code:'error',msg}
                }
                goodInfo.good.name='仙兵训练术'
            }
            let {learnSoldierNum}=getSoldierNum(itemInfo.soldiers)
            if(learnSoldierNum>=2){
                msg='训练术使用失败，每个武将只能学习2种新兵种</br>'
                return {code:'error',msg}
            }else{
                let item=null
                for (const key in soldierTemplate) {
                    if (Object.prototype.hasOwnProperty.call(soldierTemplate, key)) {
                        const element = soldierTemplate[key];
                        if(goodInfo.good.name.includes(element.name)){
                            item=element
                            break
                        }
                    }
                }
                let soldier = new SoldierEntity()
                soldier.general=itemInfo
                soldier.name = item.name
                soldier.attack = item.attack
                soldier.hp = item.hp
                soldier.defense = item.defense
                soldier.population = item.population
                soldier.userId = itemInfo.userId
                soldier.teamOrder=5
                soldier.type=item.type
                soldier.skills=[]
                item.skill.forEach((item, index) => {
                    let skillSoldier = new SkillEntity()
                    skillSoldier.name = item[0]
                    skillSoldier.damage = item[1]
                    skillSoldier.distance = item[2]
                    skillSoldier.damageNum = item[3]
                    skillSoldier.weaponType = item[4]
                    if (item[5]) {
                        skillSoldier.interval = item[5]
                    }
                    if (item[0] == '普通攻击') {
                        skillSoldier.isDefault = 1
                    }
                    skillSoldier.soldier = soldier
                    soldier.skills.push(skillSoldier)
                })
                await manager.save(soldier)
                msg=itemInfo.name+'使用'+goodInfo.good.name+'成功，学习了新兵种</br>'
                // this.eventEmitter.emit('sendMsgToRole', {type:1,content:`完成${npcInfo.dungeonName}副本，自动退出副本` })
            }
        }
        //阵法和其它
        let zhenfaList=["天外飞仙(未成)",
            "群羊阵训练术",
            "北斗阵训练术",
            "长蛇阵训练术",
            "斗底阵训练术",
            "金锁阵训练术",
            "埋伏阵训练术",
            "星观阵训练术",
            "三才阵训练术",
            "出水阵训练术",
            "血印手技能书",
            "真武秘籍",
            "天龙秘籍",
            "战歌礼赞技能书",
            "干将剑谱技能书",
            "莫邪剑谱技能书",
            "士气提升技能书",
            "怒气训练术",
            "士气训练术",
            "必杀技巧一",
            "必杀技巧二",
            "必杀技巧三",
        ]
        return {code:'ok',msg}
    }
    
}